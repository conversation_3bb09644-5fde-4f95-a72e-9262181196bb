{"tools": {"searchPlaceholder": "جستجوی ابزارها...", "noToolsAvailable": "ابزاری در دسترس نیست", "selectToolToDebug": "ابزاری برای اشکال‌زدایی انتخاب کنید", "dialogDescription": "اشکال‌زدایی و آزمایش ابزارهای فراهم‌شده توسط کارسازهای MCP", "toolsCount": "ابزارها", "availableTools": "ابزارهای در دسترس", "toolList": "فهرست ابزارها", "functionDescription": "توضیح تابع", "invalidJson": "قالب JSON نادرست", "inputHint": "لطفاً داده‌ها را در قالب JSON وارد کنید", "required": "لازم", "noDescription": "بدون توضیح", "input": "ورودی", "path": "مسیر", "pathPlaceholder": "مسیر پرونده را وارد کنید", "searchPattern": "الگوی جستجو", "searchPatternPlaceholder": "عبارت منظم را وارد کنید", "filePattern": "الگوی پرونده", "filePatternPlaceholder": "الگوی پرونده را وارد کنید، مانند: *.md", "executeButton": "اجرا", "resultTitle": "خروجی اجرا", "runningTool": "در حال اجرا...", "loading": "در حال بارگذاری...", "error": "بارگذاری ناموفق", "available": "{count} ابزار در دسترس", "none": "ابزاری در دسترسی نیست", "title": "ابزارهای MCP", "description": "ابزارهای فراهم‌شده توسط کارساز MCP", "loadError": "بارگذاری ابزارها انجام نشد", "parameters": "داده‌ها", "refresh": "تازه‌سازی", "disabled": "MCP خاموش است", "enableToUse": "لطفاً MCP را روشن کنید تا ابزارها به‌کارگرفته شود", "enabled": "روشن کردن MCP", "enabledDescription": "روشن کردن تابع MCP برای به‌کارگیری در فراخوانی ابزارها", "empty": "<PERSON>ا<PERSON><PERSON>", "jsonInputPlaceholder": "داده‌ها را در قالب JSON وارد کنید", "type": "نوع", "annotations": "حاشیه‌نویسی", "invalidJsonFormat": "قالب JSON نادرست است"}, "addServer": "افزودن کارساز", "addServerDialog": {"description": "پیکربندی کارساز جدید MCP", "title": "افزودن کارساز"}, "confirmDelete": {"cancel": "ر<PERSON> کر<PERSON>ن", "confirm": "پاک کردن", "description": "آیا مطمئن هستید که می‌خواهید کارساز {name} را پاک کنید؟ \nاین کنش را نمیتوان رد کرد.", "title": "تأیید پاک کردن"}, "confirmRemoveServer": "آیا مطمئن هستید که می‌خواهید کارساز {name} را پاک کنید؟ \nاین کنش را نمیتوان رد کرد.", "default": "پیش‌<PERSON><PERSON>ض", "deleteServer": "پاک کردن کارساز", "description": "مدیریت و پیکربندی کارسازها و ابزارهای MCP (قرارداد هدایت مدل)", "editServer": "ویرایش کارساز", "editServerDialog": {"description": "ویرایش پیکربندی کارساز MCP", "title": "ویرایش کارساز"}, "enableToAccess": "لطفاً ابتدا MCP را روشن کنید تا به گزینه‌های پیکربندی دسترسی پیدا کنید", "enabledDescription": "روشن یا خاموش کردن امکانات و ابزارهای MCP", "enabledTitle": "روشن کردن MCP", "isDefault": "کارساز پیش‌فرض", "noServersFound": "کارسازی یافت نشد", "removeDefault": "پاک کردن پیش‌فرض", "removeServer": "پاک کردن کارساز", "removeServerDialog": {"title": "پاک کردن کارساز"}, "resetConfirm": "بازیا<PERSON>ی", "resetConfirmDescription": "این کنش همه کارسازهای پیش‌فرض را بازیابی می‌کند در حالی که کارسازهای دلخواه شما نگه داشته می‌شوند. \nهرگونه دگرگونی در کارساز پیش‌فرض از دست خواهد رفت.", "resetConfirmTitle": "بازیا<PERSON><PERSON> خدمات پیش‌فرض", "resetToDefault": "بازیا<PERSON><PERSON> خدمات پیش‌فرض", "running": "در حال اجرا", "serverForm": {"add": "افزودن به", "args": "داده‌ها", "argsPlaceholder": "داده‌ها را با فاصله وارد کنید", "argsRequired": "داده‌ها نمی‌توانند خالی باشند", "autoApprove": "پذی<PERSON><PERSON> خودکار", "autoApproveAll": "همه", "autoApproveHelp": "نوع کنشی که نیاز به پذیرش خودکار دارد و بدون تأیید کاربر اجرا می‌شود را انتخاب کنید", "autoApproveRead": "<PERSON>و<PERSON><PERSON>ن", "autoApproveWrite": "نوشتن", "baseUrl": "نشانی پایه", "baseUrlPlaceholder": "نشانی پایه کارساز را وارد کنید (برای مثال: http://localhost:3000)", "cancel": "ر<PERSON> کر<PERSON>ن", "command": "دستور", "commandPlaceholder": "دستور را وارد کنید", "commandRequired": "دستور نمی‌تواند خالی باشد", "configImported": "وارد کردن پیکربندی موفق بود", "description": "توصیف", "descriptionPlaceholder": "توصیف کارساز را وارد کنید", "descriptions": "توصیف", "descriptionsPlaceholder": "توصیف کارساز را وارد کنید", "env": "متغیرهای محیط", "envInvalid": "متغیرهای محیط باید در قالب JSON معتبر باشند", "envPlaceholder": "متغیرهای محیط را در قالب JSON وارد کنید", "icon": "نماد", "iconPlaceholder": "نماد را وارد کنید", "icons": "نماد", "iconsPlaceholder": "نماد را وارد کنید", "jsonConfig": "پیکربندی JSON", "jsonConfigExample": "نمونه پیکربندی JSON", "jsonConfigIntro": "می‌توانید پیکربندی JSON را مستقیم جای‌گذاری کنید یا کارساز را به‌طور دستی پیکربندی کنید.", "jsonConfigPlaceholder": "لطفاً پیکربندی JSON کارساز MCP را جای‌گذاری کنید", "name": "نام کارساز", "namePlaceholder": "نام کارساز را وارد کنید", "nameRequired": "نام کارساز نمی‌تواند خالی باشد", "parseAndContinue": "تجزیه و ادامه", "parseError": "خطای تجزیه", "parseSuccess": "تجزیه پیکربندی موفق بود", "skipToManual": "پرش به پیکربندی دستی", "submit": "فرستادن", "folders": "فهرست پوشه‌ها", "addFolder": "افزودن پوشه", "selectFolder": "انتخاب پوشه", "selectFolderError": "انتخاب پوشه ناموفق بود", "noFoldersSelected": "هیچ پوشه‌ای انتخاب نشده", "type": "نوع کارساز", "typeInMemory": "حافظه", "typePlaceholder": "نوع کارساز را انتخاب کنید", "typeSse": "فرستادن رویدادهای کارساز", "typeStdio": "ورودی و خروجی استاندارد", "update": "به‌روزرسانی"}, "serverList": "فهرست کارسازها", "setAsDefault": "تنظیم به‌عنوان کارساز پیش‌فرض", "setDefault": "تنظیم به‌عنوان پیش‌فرض", "startServer": "راه‌اندازی کارساز", "stopServer": "توق<PERSON> کارساز", "stopped": "متوقف", "tabs": {"servers": "کار<PERSON><PERSON>ز", "tools": "ابزار"}, "title": "تنظیمات MCP", "inmemory": {"Artifacts": {"desc": "خروجی دیپ‌چت را به محتوای متنوع تبدیل کنید", "name": "محصولات"}, "bochaSearch": {"desc": "رابط جستجوی بوچا https://open.bochaai.com/", "name": "جستجوی بوچا"}, "buildInFileSystem": {"desc": "دیپ‌چت را قادر به مدیریت پرونده‌های محلی کنید", "name": "سامانه پرونده"}, "mediaServer": {"desc": "به همه مدل‌های دیپ‌چت امکان پردازش تصاویر، ویدیوها و فایل‌های صوتی را می‌دهد", "name": "سرویس رسانه"}, "braveSearch": {"desc": "رابط جستجوی بریو https://brave.com/search/api/", "name": "جستجوی بریو"}, "powerpack": {"desc": "امکانات بهبودیافته‌ای مانند پرس‌وجوی زمان، دریافت داده‌های وب و اجرای ایمن کد را برای هر مدل بزرگ فراهم می‌کند تا مدل دارای امکانات قوی‌تر و دقیق‌تر برای کسب اطلاعات باشد.", "name": "جعبه‌ابزار بهبود"}, "difyKnowledge": {"desc": "خدمات جستجوی پایگاه دانش دیفای، میتواند محتوای پایگاه دانش دیفای را بازیابی کند", "name": "جستجوی پایگاه دانش دیفای"}, "ragflowKnowledge": {"name": "جستجوی پایگاه دانش رگ‌فلو", "desc": "خدمات جستجوی پایگاه دانش رگ‌فلو، میتواند در محتوای پایگاه دانش رگ‌فلو جستجو کند"}, "fastGptKnowledge": {"name": "جستجوی پایگاه دانش فست‌جی‌پی‌تی", "desc": "خدمات جستجوی پایگاه دانش فست‌جی‌پی‌تی، میتواند در محتوای پایگاه دانش فست‌جی‌پی‌تی جستجو کند"}, "deepchat-inmemory/custom-prompts-server": {"desc": "خدمات دستورکار دلخواه داخلی دیپ‌چت", "name": "دستورکار‌های دلخواه"}, "deepchat-inmemory/deep-research-server": {"desc": "خدمات پژوهش عمیق داخلی دیپ‌چت مبتنی بر جستجوی بوچا (توجه: نیاز به مدل‌های با زمینه طولانی دارد، مدل‌های با زمینه ناکافی ممکن است شکست بخورند)", "name": "پژوهش عمیق"}, "deepchat-inmemory/auto-prompting-server": {"name": "رو<PERSON><PERSON><PERSON><PERSON> خودکار", "desc": "رویکرد دلخواه مناسب را بر پایه ورودی کاربر به‌طور هوشمندانه انتخاب کرده و الگو رویکرد را پر می‌کند."}, "deepchat-inmemory/conversation-search-server": {"name": "جستجوی تاریخچه گفت‌وگو", "desc": "خدمات جستجوی تاریخچه گفت‌وگوی داخلی دیپ‌چت، میتواند در تاریخچه و محتوای پیام‌های گفت‌وگو جستجو کند"}, "deepchat-inmemory/meeting-server": {"name": "جل<PERSON>ه چند-عامل", "desc": "سرویس جلسه داخلی DeepChat امکان میزبانی و مدیریت بحث‌های چند-عامله را فراهم می‌کند."}, "builtinKnowledge": {"desc": "خدمات جستجوی پایه دانش داخلی Deepchat ، که می تواند محتوای پایگاه دانش داخلی Deepchat را جستجو کند", "name": "جستجوی پایگاه دانش داخلی"}, "deepchat/apple-server": {"desc": "بگذارید مدل عملکردهای سیستم MACOS مانند تقویم ، مخاطبین ، ایمیل ها ، نقشه ها ، یادداشت ها ، یادآوری ها و سایر سیستم ها را کار کند", "name": "دستیار سیستم MACOS"}}, "prompts": {"noPromptsAvailable": "دستورکاری در دسترس نیست", "noDescription": "هنوز توضیحی نیست", "selectPrompt": "اینجا نمایش داده می‌شود", "parameters": "داده‌های دستور", "input": "داده‌های ورودی", "runningPrompt": "دستورکار در حال دریافت است", "executeButton": "برای دریافت اینحا بزنید", "resultTitle": "جزئیات دستورکار", "invalidJson": "قالب JSON نادرست", "parametersHint": "لطفاً داده‌ها را در قالب JSON وارد کنید، از قالب‌بندی خودکار پشتیبانی می‌کند", "resetToDefault": "بازنشانی به داده‌های پیش‌فرض", "dialogDescription": "اشکال‌زدایی و آزمایش دستورکار‌های فراهم‌شده توسط کارسازهای MCP"}, "resources": {"noResourcesAvailable": "منبعی نیست", "selectResource": "محتوای منابع اینجا نمایش داده می‌شود", "loading": "در حال بارگذاری", "loadContent": "دریافت محتوای منبع", "pleaseSelect": "برای نمایش جزئیات منابع اینجا بزنید", "dialogDescription": "مرور و مشاهده منابع فراهم‌شده توسط کارسازهای MCP"}, "errors": {"loadConfigFailed": "بارگذاری پیکربندی MCP ناموفق بود", "setEnabledFailed": "تنظیم وضعیت روشن MCP ناموفق بود", "getServerStatusFailed": "دریافت وضعیت کارساز {serverName} ناموفق بود", "addServerFailed": "افزودن کارساز ناموفق بود", "updateServerFailed": "به‌روزرسانی کارساز ناموفق بود", "removeServerFailed": "پاک کردن کارساز ناموفق بود", "maxDefaultServersReached": "به بیشینه تعداد کارسازهای پیش‌فرض (30) رسیده‌اید", "toggleDefaultServerFailed": "تغییر حالت کارساز پیش‌فرض ناموفق بود", "resetToDefaultFailed": "بازنشانی به کارسازهای پیش‌فرض ناموفق بود", "toggleServerFailed": "تغییر حالت کارساز {serverName} ناموفق بود", "loadToolsFailed": "بارگذاری ابزارها ناموفق بود", "loadPromptsFailed": "بارگذاری دستورکار‌ها ناموفق بود", "loadResourcesFailed": "بارگذاری منابع ناموفق بود", "callToolFailed": "فراخوانی ابزار {toolName} ناموفق بود", "toolCallError": "خطای فراخوانی ابزار: {error}", "mcpDisabled": "MCP خاموش است", "getPromptFailed": "دریافت دستورکار ناموفق بود", "readResourceFailed": "خواندن منبع ناموفق بود"}, "market": {"apiKeyPlaceholder": "کلید API MCPROUTER را وارد کنید", "apiKeyRequiredDesc": "لطفاً قبل از نصب ، ابتدا کلید API McProuter را پر کنید", "apiKeyRequiredTitle": "به کلید API نیاز دارد", "browseBuiltin": "مرور بازار داخلی MCP", "builtinTitle": "بازار MCP", "empty": "هنوز هیچ خدمتی وجود ندارد", "install": "نصب کردن", "installFailed": "نصب انجام نشد", "installSuccess": "نصب با موفقیت", "installed": "نصب شده", "keyGuide": "کلید را دریافت کنید", "keyHelpEnd": "پس از درخواست کلید API ، کادر ورودی را در بالا پر کنید", "keyHelpText": "لطفا ابتدا وارد شوید", "loadMore": "بار بیشتر", "noMore": "نه بیشتر", "poweredBy": "نیرو توسط McProuter", "pullDownToLoad": "برای بارگیری بیشتر به پایین بیاید"}}