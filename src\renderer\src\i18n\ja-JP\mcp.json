{"title": "MCP設定", "description": "MCP（Model Context Protocol）サーバーとツールの管理と設定", "capability": {"title": "MCP機能", "description": "MCP（Model Context Protocol）サーバー、ツール、設定の管理と設定"}, "tools": {"searchPlaceholder": "ツールを検索...", "noToolsAvailable": "利用可能なツールがありません", "toolList": "ツールリスト", "functionDescription": "機能説明", "path": "パス", "pathPlaceholder": "ファイルパスを入力", "searchPattern": "検索パターン", "searchPatternPlaceholder": "正規表現を入力", "filePattern": "ファイルパターン", "filePatternPlaceholder": "ファイルパターンを入力（例：*.md）", "executeButton": "実行", "resultTitle": "実行結果", "runningTool": "実行中...", "loading": "読み込み中...", "error": "読み込み失敗", "available": "{count}個のツールが利用可能", "none": "利用可能なツールがありません", "title": "MCPツール", "description": "MCPサーバーが提供するツール", "loadError": "ツールの読み込みに失敗しました", "parameters": "パラメータ", "refresh": "更新", "disabled": "MCPは無効です", "enableToUse": "MCPを有効にしてツールを使用", "enabled": "MCPを有効化", "enabledDescription": "ツール呼び出しを使用するためにMCP機能を有効にします", "empty": "空", "invalidJsonFormat": "無効なJSON形式", "jsonInputPlaceholder": "JSON形式でパラメータを入力", "input": "パラメータ入力", "type": "タイプ", "annotations": "注釈", "selectToolToDebug": "デバッグするツールを選択", "dialogDescription": "MCPサーバーが提供するツールのデバッグとテスト", "toolsCount": "{count} 個のツール", "availableTools": "利用可能なツール", "invalidJson": "無効なJSON形式", "inputHint": "JSON形式でパラメータを入力してください", "required": "必須", "noDescription": "説明なし"}, "addServer": "サーバーを追加", "addServerDialog": {"description": "新しいMCPサーバーを構成します", "title": "サーバーを追加"}, "confirmDelete": {"cancel": "キャンセル", "confirm": "削除", "description": "サーバー{name}を削除したいですか？この操作はキャンセルできません。", "title": "削除の確認"}, "confirmRemoveServer": "サーバー{name}を削除してもよろしいですか？この操作は取り消せません。", "default": "デフォルト", "deleteServer": "サーバーを削除", "editServer": "サーバーを編集", "editServerDialog": {"description": "MCPサーバーの構成を編集します", "title": "サーバーを編集"}, "enableToAccess": "設定オプションにアクセスするには、まずMCPを有効にしてください", "enabledDescription": "MCP機能とツールの有効化または無効化", "enabledTitle": "MCPを有効化", "isDefault": "デフォルトサーバー", "noServersFound": "サーバーが見つかりません", "removeDefault": "デフォルトを削除", "removeServer": "サーバーを削除", "removeServerDialog": {"title": "サーバーを削除"}, "resetConfirm": "復元", "resetConfirmDescription": "このアクションは、カスタマイズされたサーバーを保持しながら、すべてのデフォルトサーバーを復元します。デフォルトのサーバーの変更は失われます。", "resetConfirmTitle": "デフォルトサービスを復元", "resetToDefault": "デフォルトサービスを復元", "running": "ランニング", "serverForm": {"add": "追加", "args": "パラメーター", "argsPlaceholder": "スペースで区切られたパラメーターを入力します", "argsRequired": "パラメーターを空にすることはできません", "autoApprove": "自動認証", "autoApproveAll": "全て", "autoApproveHelp": "自動認証が必要な操作タイプを選択し、ユーザー確認なしで実行する", "autoApproveRead": "読む", "autoApproveWrite": "書く", "baseUrl": "基本URL", "baseUrlPlaceholder": "サーバーの基本URLを入力します（例：http：// localhost：3000）", "cancel": "キャンセル", "command": "注文", "commandPlaceholder": "コマンドを入力します", "commandRequired": "コマンドを空にすることはできません", "configImported": "構成インポートが成功しました", "description": "説明", "descriptionPlaceholder": "サーバーの説明を入力", "descriptions": "説明", "descriptionsPlaceholder": "サーバーの説明を入力", "env": "環境変数", "envInvalid": "環境変数は有効なJSON形式でなければなりません", "envPlaceholder": "JSON形式で環境変数を入力します", "icon": "アイコン", "iconPlaceholder": "アイコンを入力します", "icons": "アイコン", "iconsPlaceholder": "アイコンを入力します", "jsonConfig": "JSON構成", "jsonConfigExample": "JSON構成の例", "jsonConfigIntro": "JSON構成を直接貼り付けるか、サーバーを手動で構成することを選択できます。", "jsonConfigPlaceholder": "MCPサーバーのJSON形式の構成を貼り付けてください", "name": "サーバー名", "namePlaceholder": "サーバー名を入力します", "nameRequired": "サーバー名を空にすることはできません", "parseAndContinue": "分析して続行します", "parseError": "解析エラー", "parseSuccess": "設定の解析に成功しました", "skipToManual": "手動構成までスキップします", "submit": "送信", "type": "サーバータイプ", "typeInMemory": "メモリ", "typePlaceholder": "サーバータイプを選択します", "typeSse": "サーバー送信イベント（SSE）", "typeStdio": "標準入力と出力", "update": "更新します", "addFolder": "フォルダーを追加します", "folders": "フォルダー一覧", "noFoldersSelected": "フォルダーは選択されていません", "selectFolder": "フォルダーを選択します", "selectFolderError": "フォルダーの選択に失敗しました"}, "serverList": "サーバー一覧", "setAsDefault": "デフォルトサーバーに設定", "setDefault": "デフォルトに設定", "startServer": "サーバーを起動", "stopServer": "サーバーを停止", "stopped": "停止", "tabs": {"servers": "サーバ", "tools": "ツール"}, "inmemory": {"Artifacts": {"desc": "Deepchat出力の多様化されたアーティファクトコンテンツを作成します", "name": "Artifacts"}, "bochaSearch": {"desc": "Bocha検索API https://open.bochaai.com/", "name": "Bocha Search"}, "buildInFileSystem": {"desc": "DeepChatがローカルファイルを操作できるようにします", "name": "ファイルシステム"}, "mediaServer": {"desc": "有効にすると、DeepChatのすべてのモデルが画像、動画、音声ファイルを処理できるようになります", "name": "メディアサービス"}, "braveSearch": {"desc": "勇敢な検索API https://brave.com/search/api/", "name": "勇敢な検索"}, "powerpack": {"desc": "モデルがより強力で正確な情報収集機能を持つように、タイムクエリ、Web情報取得、セキュアコード実行などの強化された機能を提供します。", "name": "拡張ツールキット"}, "difyKnowledge": {"name": "Dify ナレッジベース検索", "desc": "Dify ナレッジベース検索サービス。Dify ナレッジベースのコンテンツを検索できます。"}, "ragflowKnowledge": {"name": "RAGFlow知識ベース検索", "desc": "RAGFlow知識ベース検索サービス、RAGFlow知識ベースのコンテンツを検索できます"}, "fastGptKnowledge": {"name": "FastGPT知識ベース検索", "desc": "FastGPT知識ベース検索サービス、FastGPT知識ベースのコンテンツを検索できます"}, "deepchat-inmemory/custom-prompts-server": {"desc": "Deepchatビルトインカスタムプロンプトワードサービス", "name": "カスタムプロンプトワード"}, "deepchat-inmemory/deep-research-server": {"desc": "Bocha検索に基づくDeepchatビルトイン詳細な研究サービス（長いコンテキストモデルを使用する必要があり、コンテキストが不十分なモデルが失敗する可能性があることに注意してください）", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "自動テンプレートプロンプト", "desc": "ユーザー入力に基づいて最適なカスタムプロンプトを自動的に選択し、プロンプトテンプレートをインテリジェントに埋め込みます。"}, "deepchat-inmemory/conversation-search-server": {"name": "会話履歴検索", "desc": "DeepChat内蔵の会話履歴検索サービス、過去の会話記録とメッセージ内容を検索できます"}, "deepchat-inmemory/meeting-server": {"name": "マルチエージェント会議", "desc": "DeepChatの内蔵会議サービスは、マルチエージェントによる討論の開催と進行を可能にします。"}, "builtinKnowledge": {"desc": "deepchatビルトインナレッジベース検索サービス。これは、deepchatビルトインナレッジベースのコンテンツを検索できる", "name": "組み込みのナレッジベース検索"}, "deepchat/apple-server": {"desc": "モデルがカレンダー、連絡先、電子メール、マップ、メモ、リマインダー、その他のシステムなどのMacOSのシステム機能を動作させます", "name": "MACOSシステムアシスタント"}}, "prompts": {"noPromptsAvailable": "利用可能なプロンプトはありません", "noDescription": "まだ説明はありません", "selectPrompt": "ここに示されています", "parameters": "プロンプトパラメーター", "input": "入力パラメーター", "runningPrompt": "プロンプトが取得されています", "executeButton": "クリックして取得します", "resultTitle": "詳細を迅速にします", "invalidJson": "無効なJSON形式", "parametersHint": "JSON形式のパラメーターを入力してください。自動フォーマットをサポートしてください", "resetToDefault": "デフォルトのパラメーターにリセットします", "dialogDescription": "MCPサーバーが提供するプロンプトをデバッグおよびテストします"}, "resources": {"noResourcesAvailable": "なしリソース", "selectResource": "ここにリソースのコンテンツを表示します", "loading": "読み込み", "loadContent": "リソースコンテンツを取得します", "pleaseSelect": "クリックして表示リソースの詳細を取得します", "dialogDescription": "MCPサーバーが提供するリソースを参照および表示します"}, "errors": {"loadConfigFailed": "MCP設定の読み込みに失敗しました", "setEnabledFailed": "MCPの有効状態の設定に失敗しました", "getServerStatusFailed": "サーバー {serverName} の状態の取得に失敗しました", "addServerFailed": "サーバーの追加に失敗しました", "updateServerFailed": "サーバーの更新に失敗しました", "removeServerFailed": "サーバーの削除に失敗しました", "maxDefaultServersReached": "デフォルトサーバーの最大数（30）に達しました", "toggleDefaultServerFailed": "デフォルトサーバーの状態の切り替えに失敗しました", "resetToDefaultFailed": "デフォルトサーバーへのリセットに失敗しました", "toggleServerFailed": "サーバー {serverName} の状態の切り替えに失敗しました", "loadToolsFailed": "ツールの読み込みに失敗しました", "loadPromptsFailed": "プロンプトの読み込みに失敗しました", "loadResourcesFailed": "リソースの読み込みに失敗しました", "callToolFailed": "ツール {toolName} の呼び出しに失敗しました", "toolCallError": "ツール呼び出しエラー: {error}", "mcpDisabled": "MCPは無効になっています", "getPromptFailed": "プロンプトの取得に失敗しました", "readResourceFailed": "リソースの読み込みに失敗しました"}, "market": {"browseBuiltin": "内蔵MCPマーケットを閲覧", "builtinTitle": "MCP市場", "poweredBy": "Powered by <PERSON><PERSON><PERSON><PERSON>", "keyGuide": "APIキーを取得", "keyHelpText": "まず", "keyHelpEnd": "でAPIキーを申請し、上記の入力ボックスに入力してください", "apiKeyPlaceholder": "MCPRouter APIキーを入力", "apiKeyRequiredTitle": "APIキーが必要", "apiKeyRequiredDesc": "インストール前にMCPRouter APIキーを入力してください", "install": "インストール", "installed": "インストール済み", "installSuccess": "インストール成功", "installFailed": "インストール失敗", "noMore": "これ以上ありません", "empty": "サービスがありません", "loadMore": "さらに読み込み", "pullDownToLoad": "下にスクロールしてさらに読み込み"}}