{"tools": {"searchPlaceholder": "搜索工具...", "noToolsAvailable": "暫無可用工具", "toolList": "工具列表", "functionDescription": "功能描述", "path": "路徑", "pathPlaceholder": "輸入文件路徑", "searchPattern": "搜索模式", "searchPatternPlaceholder": "輸入正則表達式", "filePattern": "文件模式", "filePatternPlaceholder": "輸入文件模式，例如: *.md", "executeButton": "執行工具", "resultTitle": "執行結果", "runningTool": "正在執行工具", "loading": "加載中...", "error": "加載失敗", "available": "{count}個工具可用", "none": "沒有可用工具", "title": "MCP工具", "description": "MCP服務器提供的工具", "loadError": "加載工具失敗", "parameters": "參數", "refresh": "刷新", "disabled": "MCP已禁用", "enableToUse": "請先啟用MCP以使用工具", "enabled": "啟用MCP", "enabledDescription": "啟用MCP功能以使用工具調用", "empty": "空", "invalidJsonFormat": "無效的JSON格式", "jsonInputPlaceholder": "輸入JSON格式的參數", "input": "輸入參數", "type": "類型", "annotations": "註解", "selectToolToDebug": "選擇要調試的工具", "dialogDescription": "調試和測試 MCP 服務器提供的工具", "toolsCount": "工具", "availableTools": "可用工具", "invalidJson": "無效的 JSON 格式", "inputHint": "請以 JSON 格式輸入參數", "required": "必填", "noDescription": "無描述"}, "addServer": "添加服務器", "addServerDialog": {"description": "配置新的MCP服務器", "title": "添加服務器"}, "confirmDelete": {"cancel": "取消", "confirm": "刪除", "description": "確定要刪除服務器 {name} 嗎？\n此操作無法撤銷。", "title": "確認刪除"}, "confirmRemoveServer": "確定要刪除服務器 {name} 嗎？\n此操作無法撤銷。", "default": "預設", "deleteServer": "刪除服務器", "description": "管理和配置MCP（Model Context Protocol）伺服器和工具", "editServer": "編輯服務器", "editServerDialog": {"description": "編輯MCP服務器配置", "title": "編輯服務器"}, "enableToAccess": "請先啟用MCP以訪問配置選項", "enabledDescription": "啟用或禁用MCP功能和工具", "enabledTitle": "啟用MCP", "isDefault": "默認服務器", "noServersFound": "未找到服務器", "removeDefault": "移除默認", "removeServer": "刪除服務器", "removeServerDialog": {"title": "刪除服務器"}, "resetConfirm": "恢復", "resetConfirmDescription": "此操作將恢復所有默認服務器，同時保留您自定義的服務器。\n對默認服務器的任何修改將會丟失。", "resetConfirmTitle": "恢復默認服務", "resetToDefault": "恢復默認服務", "running": "運行中", "serverForm": {"add": "添加", "args": "參數", "argsPlaceholder": "輸入參數，用空格分隔", "argsRequired": "參數不能為空", "autoApprove": "自動授權", "autoApproveAll": "全部", "autoApproveHelp": "選擇需要自動授權的操作類型，無需用戶確認即可執行", "autoApproveRead": "讀取", "autoApproveWrite": "寫入", "baseUrl": "基礎URL", "baseUrlPlaceholder": "輸入服務器基礎URL（如：http://localhost:3000）", "cancel": "取消", "command": "命令", "commandPlaceholder": "輸入命令", "commandRequired": "命令不能為空", "configImported": "配置導入成功", "description": "描述", "descriptionPlaceholder": "輸入服務器描述", "descriptions": "描述", "descriptionsPlaceholder": "輸入服務器描述", "env": "環境變量", "envInvalid": "環境變量必須是有效的JSON格式", "envPlaceholder": "輸入JSON格式的環境變量", "icon": "圖示", "iconPlaceholder": "輸入圖標", "icons": "圖示", "iconsPlaceholder": "輸入圖標", "jsonConfig": "JSON配置", "jsonConfigExample": "JSON配置示例", "jsonConfigIntro": "您可以直接粘貼JSON配置或選擇手動配置服務器。", "jsonConfigPlaceholder": "請粘貼MCP服務器的JSON格式配置", "name": "服務器名稱", "namePlaceholder": "輸入服務器名稱", "nameRequired": "服務器名稱不能為空", "parseAndContinue": "解析並繼續", "parseError": "解析錯誤", "parseSuccess": "配置解析成功", "skipToManual": "跳過至手動配置", "submit": "提交", "type": "服務器類型", "typeInMemory": "內存", "typePlaceholder": "選擇服務器類型", "typeSse": "服務器發送事件", "typeStdio": "標準輸入輸出", "update": "更新", "addFolder": "添加文件夾", "folders": "文件夾列表", "noFoldersSelected": "未選擇任何文件夾", "selectFolder": "選擇文件夾", "selectFolderError": "選擇文件夾失敗"}, "serverList": "服務器列表", "setAsDefault": "設為默認服務器", "setDefault": "設為默認", "startServer": "啟動服務器", "stopServer": "停止服務器", "stopped": "已停止", "tabs": {"servers": "伺服器", "tools": "工具"}, "title": "MCP設定", "inmemory": {"Artifacts": {"desc": "讓你的 DeepChat 輸出的多樣化的 Artifacts 內容", "name": "Artifacts"}, "bochaSearch": {"desc": "博查搜索 API https://open.bochaai.com/", "name": "博查搜索"}, "buildInFileSystem": {"desc": "讓 DeepChat 能夠操作本地文件", "name": "文件系統"}, "mediaServer": {"desc": "開啟後 DeepChat 中任意模型都能處理圖片、視頻、音頻等媒體文件", "name": "媒體服務"}, "braveSearch": {"desc": "Brave搜索 API https://brave.com/search/api/", "name": "Brave搜索"}, "powerpack": {"desc": "為任意大模型提供時間查詢、網頁信息獲取和安全的代碼執行等增強能力，讓模型擁有更強大和準確的信息獲取能力", "name": "增強工具包"}, "difyKnowledge": {"name": "Dify知識庫檢索", "desc": "Dify知識庫檢索服務，可以檢索Dify知識庫中的內容"}, "ragflowKnowledge": {"name": "RAGFlow知識庫檢索", "desc": "RAGFlow知識庫檢索服務，可以對RAGFlow知識庫內容進行檢索"}, "fastGptKnowledge": {"name": "FastGPT知識庫檢索", "desc": "FastGPT知識庫檢索服務，可以對FastGPT知識庫內容進行檢索"}, "deepchat-inmemory/custom-prompts-server": {"desc": "DeepChat內置自定義提示詞服務", "name": "自定義提示詞"}, "deepchat-inmemory/deep-research-server": {"desc": "基於博查搜索的DeepChat內置深度研究服務，（注意需要長上下文模型才能使用，上下文不足的模型可能會失敗）", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "自動模板提示詞", "desc": "根據用戶輸入自動揀選最合適的自訂提示詞，並智能填寫提示詞模板"}, "deepchat-inmemory/conversation-search-server": {"name": "對話歷史搜尋", "desc": "DeepChat內建對話歷史搜尋服務，可搜尋歷史對話記錄和訊息內容"}, "deepchat-inmemory/meeting-server": {"name": "多智能體會議", "desc": "DeepChat 內置的會議功能支援舉辦和主持多智能體討論。"}, "builtinKnowledge": {"desc": "DeepChat內置知識庫檢索服務，可以對DeepChat內置知識庫內容進行檢索", "name": "內置知識庫檢索"}, "deepchat/apple-server": {"desc": "讓模型能操作macOS的日曆、聯繫人、郵件、地圖、備忘錄、提醒事項等系統功能", "name": "macOS系統助手"}}, "prompts": {"noPromptsAvailable": "暫無可用的 Prompts", "noDescription": "暫無描述", "selectPrompt": "此處展示選中的Prompt", "parameters": "Prompt 參數", "input": "輸入參數", "runningPrompt": "Prompt 獲取中", "executeButton": "點擊獲取", "resultTitle": "Prompt 詳情", "invalidJson": "無效的 JSON 格式", "parametersHint": "請輸入 JSON 格式的參數，支持自動格式化", "resetToDefault": "重置為默認參數", "dialogDescription": "調試和測試MCP伺服器提供的提示模板"}, "resources": {"noResourcesAvailable": "暫無 Resources", "selectResource": "此處展示 Resources 內容", "loading": "載入中", "loadContent": "獲取 Resource 內容", "pleaseSelect": "點擊獲取展示Resources詳情", "dialogDescription": "瀏覽和查看MCP伺服器提供的資源"}, "errors": {"loadConfigFailed": "載入MCP配置失敗", "setEnabledFailed": "設定MCP啟用狀態失敗", "getServerStatusFailed": "獲取伺服器 {serverName} 狀態失敗", "addServerFailed": "新增伺服器失敗", "updateServerFailed": "更新伺服器失敗", "removeServerFailed": "刪除伺服器失敗", "maxDefaultServersReached": "最多只能設定30個預設伺服器", "toggleDefaultServerFailed": "切換預設伺服器狀態失敗", "resetToDefaultFailed": "恢復預設伺服器失敗", "toggleServerFailed": "切換伺服器 {serverName} 狀態失敗", "loadToolsFailed": "載入工具失敗", "loadPromptsFailed": "載入提示模板失敗", "loadResourcesFailed": "載入資源失敗", "callToolFailed": "呼叫工具 {toolName} 失敗", "toolCallError": "工具呼叫錯誤: {error}", "mcpDisabled": "MCP功能已禁用", "getPromptFailed": "獲取提示模板失敗", "readResourceFailed": "讀取資源失敗"}, "market": {"apiKeyPlaceholder": "輸入 MCPRouter API Key", "apiKeyRequiredDesc": "請先填寫 MCPRouter API Key 後再安裝", "apiKeyRequiredTitle": "需要 API Key", "browseBuiltin": "瀏覽內置 MCP 市場", "builtinTitle": "MCP 市場", "empty": "暫無服務", "install": "安裝", "installFailed": "安裝失敗", "installSuccess": "安裝成功", "installed": "已安裝", "keyGuide": "獲取密鑰", "keyHelpEnd": "申請 API Key 後填寫到上方輸入框", "keyHelpText": "請先到", "loadMore": "加載更多", "noMore": "沒有更多了", "poweredBy": "Powered by <PERSON><PERSON><PERSON><PERSON>", "pullDownToLoad": "繼續下拉加載更多"}}