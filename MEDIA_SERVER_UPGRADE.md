# 图片服务升级为媒体服务

## 概述

本次更新将原有的图片服务（ImageServer）升级为媒体服务（MediaServer），新增支持视频和音频文件的上传和处理功能。

## 主要更改

### 1. 新增文件

- `src/main/presenter/filePresenter/VideoFileAdapter.ts` - 视频文件适配器
- `src/main/presenter/mcpPresenter/inMemoryServers/mediaServer.ts` - 媒体服务器（替代原imageServer.ts）

### 2. 更新的文件

#### 文件适配器相关
- `src/main/presenter/filePresenter/mime.ts` - 添加视频格式MIME类型映射
- `src/main/presenter/filePresenter/FileAdapterConstructor.ts` - 添加VideoFileAdapter类型

#### 服务器配置相关
- `src/main/presenter/mcpPresenter/inMemoryServers/builder.ts` - 更新服务器构建器
- `src/main/presenter/configPresenter/mcpConfHelper.ts` - 更新服务器配置

#### 前端组件相关
- `src/renderer/src/components/mcp-config/mcpServerForm.vue` - 更新表单验证逻辑

#### 国际化文件
- `src/renderer/src/i18n/zh-CN/mcp.json` - 添加中文翻译
- `src/renderer/src/i18n/zh-HK/mcp.json` - 添加繁体中文翻译
- `src/renderer/src/i18n/zh-TW/mcp.json` - 添加繁体中文（台湾）翻译
- `src/renderer/src/i18n/en-US/mcp.json` - 添加英文翻译
- `src/renderer/src/i18n/ko-KR/mcp.json` - 添加韩语翻译
- `src/renderer/src/i18n/ja-JP/mcp.json` - 添加日语翻译
- `src/renderer/src/i18n/fr-FR/mcp.json` - 添加法语翻译
- `src/renderer/src/i18n/ru-RU/mcp.json` - 添加俄语翻译
- `src/renderer/src/i18n/fa-IR/mcp.json` - 添加波斯语翻译

### 3. 删除的文件

- `src/main/presenter/mcpPresenter/inMemoryServers/imageServer.ts` - 原图片服务器文件

## 新增功能

### 支持的媒体格式

#### 视频格式
- MP4 (`video/mp4`)
- AVI (`video/avi`, `video/x-msvideo`)
- MOV (`video/quicktime`)
- WMV (`video/x-ms-wmv`)
- FLV (`video/x-flv`)
- WebM (`video/webm`)
- MKV (`video/x-matroska`)
- 通配符支持 (`video/*`)

#### 音频格式（扩展）
- MP3 (`audio/mp3`, `audio/mpeg`)
- WAV (`audio/wav`, `audio/x-wav`)
- M4A (`audio/m4a`, `audio/x-m4a`)
- AAC (`audio/aac`)
- OGG (`audio/ogg`)
- FLAC (`audio/flac`)
- 通配符支持 (`audio/*`)

### 新增工具

MediaServer提供以下工具：

1. **read_media_base64** - 读取媒体文件并返回base64编码内容
2. **upload_media** - 上传媒体文件到托管服务
3. **read_multiple_media_base64** - 批量读取多个媒体文件
4. **upload_multiple_media** - 批量上传多个媒体文件
5. **get_media_info** - 获取媒体文件详细信息（新增）
6. **describe_image** - 描述图片内容（保留）
7. **query_image_with_prompt** - 基于提示词查询图片（保留）
8. **ocr_image** - 图片OCR文字识别（保留）

### 显示名称更新

在所有支持的语言中添加了mediaServer的显示名称翻译：

- **中文（简体）**: 媒体服务
- **中文（繁体香港）**: 媒體服務
- **中文（繁体台湾）**: 媒體服務
- **英语**: Media Service
- **韩语**: 미디어 서비스
- **日语**: メディアサービス
- **法语**: Service multimédia
- **俄语**: Медиа-сервис
- **波斯语**: سرویس رسانه

### 技术改进

1. **动态MIME类型检测** - 上传时自动检测文件的MIME类型
2. **增加超时时间** - 媒体文件上传超时时间从60秒增加到120秒
3. **文件信息获取** - 新增获取媒体文件元数据的功能
4. **向后兼容** - 保持对原有imageServer配置的兼容性
5. **多语言支持** - 在所有支持的语言中添加了完整的翻译

## 配置说明

### 服务器配置

在MCP服务器配置中，可以使用以下名称：
- `mediaServer` - 新的媒体服务器
- `imageServer` - 向后兼容的别名

### 自动批准工具

默认自动批准以下工具（无需用户确认）：
- `read_media_base64`
- `read_multiple_media_base64`
- `get_media_info`

需要用户确认的工具：
- `upload_media`
- `upload_multiple_media`
- `describe_image`
- `query_image_with_prompt`
- `ocr_image`

## 使用示例

### 获取媒体文件信息
```json
{
  "tool": "get_media_info",
  "arguments": {
    "path": "/path/to/video.mp4"
  }
}
```

### 上传视频文件
```json
{
  "tool": "upload_media",
  "arguments": {
    "path": "/path/to/video.mp4"
  }
}
```

### 批量上传媒体文件
```json
{
  "tool": "upload_multiple_media",
  "arguments": {
    "paths": [
      "/path/to/image.jpg",
      "/path/to/video.mp4",
      "/path/to/audio.mp3"
    ]
  }
}
```

## 注意事项

1. **文件大小限制** - 媒体文件通常较大，建议注意上传文件的大小限制
2. **网络超时** - 大文件上传可能需要更长时间，已将超时时间调整为120秒
3. **存储空间** - 视频和音频文件占用存储空间较大，请注意服务器存储容量
4. **格式支持** - 目前支持常见的媒体格式，如需支持其他格式可在mime.ts中添加

## 测试验证

项目已通过TypeScript类型检查和构建测试，确保所有更改都能正常工作。建议在实际使用前进行以下测试：

1. 测试各种媒体格式的上传功能
2. 验证文件信息获取功能
3. 确认批量操作的稳定性
4. 检查向后兼容性
