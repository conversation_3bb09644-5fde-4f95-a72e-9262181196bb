{"tools": {"searchPlaceholder": "Rechercher des outils...", "noToolsAvailable": "Aucun outil disponible", "toolList": "Liste des outils", "functionDescription": "Description de la fonction", "path": "Chemin", "pathPlaceholder": "En<PERSON><PERSON> le chemin du fichier", "searchPattern": "<PERSON><PERSON><PERSON><PERSON> de recherche", "searchPatternPlaceholder": "Entrez une expression régulière", "filePattern": "<PERSON><PERSON><PERSON><PERSON>", "filePatternPlaceholder": "Entrez le modèle de fi<PERSON>, par exemple : *.md", "executeButton": "Exécuter", "resultTitle": "Résultat de l'exécution", "runningTool": "Exécution...", "loading": "Chargement...", "error": "Échec du chargement", "available": "{count} outils disponibles", "none": "Aucun outil disponible", "title": "Outils MCP", "description": "<PERSON>ils fournis par le serveur MCP", "loadError": "Échec du chargement des outils", "parameters": "Paramètres", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "MCP est désactivé", "enableToUse": "Veuillez activer MCP pour utiliser les outils", "enabled": "Activer MCP", "enabledDescription": "Activer la fonctionnalité MCP pour utiliser les appels d'outils", "empty": "Vide", "invalidJsonFormat": "Format JSON invalide", "jsonInputPlaceholder": "Entrez les paramètres au format JSON", "input": "Paramètres d'entrée", "type": "Type", "annotations": "Annotations", "selectToolToDebug": "Sélectionnez un outil à déboguer", "dialogDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> et tester les outils fournis par les serveurs MCP", "toolsCount": "{count} outils", "availableTools": "Outils disponibles", "invalidJson": "Format JSON invalide", "inputHint": "Veuillez entrer les paramètres au format JSON", "required": "Requis", "noDescription": "Pas de description"}, "addServer": "Ajouter un serveur", "addServerDialog": {"description": "Configurer un nouveau serveur MCP", "title": "Ajouter un serveur"}, "confirmDelete": {"cancel": "Annuler", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "description": "Êtes-vous sûr de vouloir supprimer le serveur {name} ? \nCette opération ne peut pas être annulée.", "title": "Confirmer la <PERSON>"}, "confirmRemoveServer": "Êtes-vous sûr de vouloir supprimer le serveur {name} ? \nCette opération ne peut pas être annulée.", "default": "<PERSON><PERSON> <PERSON><PERSON>", "deleteServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "description": "Gérer et configurer les serveurs et outils MCP (Modèle de Contrôle de Protocole)", "editServer": "Modifier le serveur", "editServerDialog": {"description": "Modifier la configuration du serveur MCP", "title": "Modifier le serveur"}, "enableToAccess": "Veuillez d'abord activer MCP pour accéder aux options de configuration", "enabledDescription": "<PERSON>r ou désactiver les fonctionnalités et outils MCP", "enabledTitle": "Activer MCP", "isDefault": "Serveur par défaut", "noServersFound": "Serveur non trouvé", "removeDefault": "Re<PERSON>rer le statut par défaut", "removeServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "removeServerDialog": {"title": "<PERSON><PERSON><PERSON><PERSON> le serveur"}, "resetConfirm": "<PERSON><PERSON><PERSON>", "resetConfirmDescription": "Cette action restaure tous les serveurs par défaut tout en conservant vos serveurs personnalisés. \nToute modification du serveur par défaut sera perdue.", "resetConfirmTitle": "Restaurer le service par défaut", "resetToDefault": "Restaurer le service par défaut", "running": "En cours d'exécution", "serverForm": {"add": "Ajouter", "args": "Arguments", "argsPlaceholder": "Entrez les paramètres, séparés par des espaces", "argsRequired": "Les paramètres ne peuvent pas être vides", "autoApprove": "Autorisation automatique", "autoApproveAll": "<PERSON>ut", "autoApproveHelp": "Sélectionnez le type d'opération nécessitant une autorisation automatique et exécutez sans confirmation de l'utilisateur", "autoApproveRead": "Lecture", "autoApproveWrite": "Écriture", "baseUrl": "URL de base", "baseUrlPlaceholder": "Entrez l'URL de base du serveur (par exemple : http://localhost:3000)", "cancel": "Annuler", "command": "Commande", "commandPlaceholder": "Entrez une commande", "commandRequired": "La commande ne peut pas être vide", "configImported": "Importation de la configuration réussie", "description": "Description", "descriptionPlaceholder": "En<PERSON>z la description du serveur", "descriptions": "Description", "descriptionsPlaceholder": "En<PERSON>z la description du serveur", "env": "Variables d'environnement", "envInvalid": "Les variables d'environnement doivent être au format JSON valide", "envPlaceholder": "Entrez les variables d'environnement au format JSON", "icon": "Icône", "iconPlaceholder": "Entrez l'icône", "icons": "Icônes", "iconsPlaceholder": "Entrez les icônes", "jsonConfig": "Configuration JSON", "jsonConfigExample": "Exemple de configuration JSON", "jsonConfigIntro": "<PERSON><PERSON> pouvez coller directement la configuration JSON ou choisir de configurer le serveur manuellement.", "jsonConfigPlaceholder": "Veuillez coller la configuration au format JSON du serveur MCP", "name": "Nom du serveur", "namePlaceholder": "Entrez le nom du serveur", "nameRequired": "Le nom du serveur ne peut pas être vide", "parseAndContinue": "Analyser et continuer", "parseError": "<PERSON><PERSON><PERSON> d'analyse", "parseSuccess": "Configuration analysée avec succès", "skipToManual": "Passer à la configuration manuelle", "submit": "So<PERSON><PERSON><PERSON>", "type": "Type de serveur", "typeInMemory": "M<PERSON><PERSON><PERSON>", "typePlaceholder": "Sélectionnez un type de serveur", "typeSse": "Server‑Sent Events (SSE)", "typeStdio": "Entrée et sortie standard", "update": "Mettre à jour", "addFolder": "Ajouter un dossier", "folders": "Dossiers autorisés", "noFoldersSelected": "Aucun dossier n'a été sélectionné", "selectFolder": "Sélectionnez un dossier", "selectFolderError": "Échec de la sélection d'un dossier"}, "serverList": "Liste des serveurs", "setAsDefault": "Définir comme serveur par défaut", "setDefault": "Définir par défaut", "startServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "stopServer": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"servers": "serveur", "tools": "outil"}, "title": "Paramètres MCP", "inmemory": {"Artifacts": {"desc": "Rendez votre sortie DeepChat diversifiée avec du contenu d'Artifacts", "name": "Artifacts"}, "bochaSearch": {"desc": "API de Bocha Search https://open.bochaai.com/", "name": "Recherche Bocha"}, "buildInFileSystem": {"desc": "Permettre à DeepChat de manipuler des fichiers locaux", "name": "Système de fichiers"}, "mediaServer": {"desc": "Permet à tous les modèles DeepChat de traiter les images, vidéos et fichiers audio", "name": "Service multimédia"}, "braveSearch": {"desc": "Brave Search API https://brave.com/search/api/", "name": "Brave Search"}, "powerpack": {"desc": "Fournissez des capacités améliorées telles que la requête temporelle, l'acquisition d'informations Web et l'exécution de code sécurisé pour tout grand modèle, afin que le modèle ait des capacités d'acquisition d'informations plus puissantes et précises.", "name": "Boîte à outils d'amélioration"}, "difyKnowledge": {"name": "Recherche dans la base de connaissances Dify", "desc": "Service de recherche de la base de connaissances Dify, permet de rechercher le contenu de la base de connaissances Dify"}, "ragflowKnowledge": {"name": "Recherche dans la base de connaissances RAGFlow", "desc": "Service de recherche dans la base de connaissances RAGFlow, peut rechercher le contenu de la base de connaissances RAGFlow"}, "fastGptKnowledge": {"name": "Recherche dans la base de connaissances FastGPT", "desc": "Service de recherche dans la base de connaissances FastGPT, peut rechercher le contenu de la base de connaissances FastGPT"}, "deepchat-inmemory/custom-prompts-server": {"desc": "Service de prompts personnalisés intégré à DeepChat", "name": "Prompts personnalis<PERSON>"}, "deepchat-inmemory/deep-research-server": {"desc": "Fonction de recherche approfondie intégrée, basée sur Bocha Search. Modèles à long contexte recommandés.", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "Auto‑prompt par modèle", "desc": "Sélection automatique du prompt le plus adapté selon l’entrée, et remplissage intelligent du gabarit."}, "deepchat-inmemory/conversation-search-server": {"name": "Recherche d’historique", "desc": "Rechercher les conversations et messages passés (intégré à DeepChat)"}, "deepchat-inmemory/meeting-server": {"name": "Réunion Multi-Agent", "desc": "Le service de réunion intégré de DeepChat permet d’organiser et d’animer des discussions multi-agents."}, "builtinKnowledge": {"desc": "Recherche dans la base de connaissances intégrée de DeepChat (docs, contenus intégrés).", "name": "Base de connaissances intégrée"}, "deepchat/apple-server": {"desc": "Laissez le modèle d'exploitation des fonctions du système de MacOS telles que le calendrier, les contacts, les e-mails, les cartes, les mémos, les rappels et autres systèmes", "name": "Assistant système macOS"}}, "prompts": {"noPromptsAvailable": "Aucune invite disponible", "noDescription": "Aucune description encore", "selectPrompt": "<PERSON><PERSON> ici", "parameters": "Paramètres rapides", "input": "Paramètres d'entrée", "runningPrompt": "Invite obtient", "executeButton": "Cliquez pour obtenir", "resultTitle": "Des détails proches", "invalidJson": "Format JSON non valide", "parametersHint": "Veuillez saisir les paramètres au format JSON, prendre en charge la mise en forme automatique", "resetToDefault": "Réinitialiser avec les paramètres par défaut", "dialogDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> et tester les prompts fournis par les serveurs MCP"}, "resources": {"noResourcesAvailable": "<PERSON><PERSON>ne ressource", "selectResource": "Sélectionnez une ressource pour afficher le contenu", "loading": "Chargement...", "loadContent": "Charger le contenu", "pleaseSelect": "Cliquer pour afficher les détails de la ressource", "dialogDescription": "Parcourir et afficher les ressources fournies par les serveurs MCP"}, "errors": {"loadConfigFailed": "Échec du chargement de la configuration MCP", "setEnabledFailed": "Échec de la modification de l'état d'activation de MCP", "getServerStatusFailed": "Échec de l'obtention du statut du serveur {serverName}", "addServerFailed": "Échec de l'ajout du serveur", "updateServerFailed": "Échec de la mise à jour du serveur", "removeServerFailed": "Échec de la suppression du serveur", "maxDefaultServersReached": "Nombre maximum de serveurs par défaut (30) atteint", "toggleDefaultServerFailed": "Échec du changement d'état du serveur par défaut", "resetToDefaultFailed": "Échec de la réinitialisation des serveurs par défaut", "toggleServerFailed": "Échec du changement d'état du serveur {serverName}", "loadToolsFailed": "Échec du chargement des outils", "loadPromptsFailed": "Échec du chargement des prompts", "loadResourcesFailed": "Échec du chargement des ressources", "callToolFailed": "Échec de l'appel de l'outil {toolName}", "toolCallError": "Erreur d'appel d'outil : {error}", "mcpDisabled": "MCP est désactivé", "getPromptFailed": "Échec de l'obtention du prompt", "readResourceFailed": "Échec de la lecture de la ressource"}, "market": {"apiKeyPlaceholder": "Entrez la clé de l'API McProuter", "apiKeyRequiredDesc": "Veuillez remplir la clé API McProuter avant d'installer", "apiKeyRequiredTitle": "Nécessite la clé de l'API", "browseBuiltin": "Parcour<PERSON> le marché MCP intégré", "builtinTitle": "<PERSON><PERSON>", "empty": "Pas encore de service", "install": "Installer", "installFailed": "L'installation a échoué", "installSuccess": "Installation avec succès", "installed": "Installé", "keyGuide": "Obtenez la clé", "keyHelpEnd": "Après avoir postulé pour la clé API, remplissez la zone d'entrée ci-dessus", "keyHelpText": "Veuillez arriver en premier", "loadMore": "Chargez plus", "noMore": "Pas plus", "poweredBy": "Propulsé par <PERSON><PERSON><PERSON><PERSON><PERSON>", "pullDownToLoad": "Continuez à tomber pour charger plus"}}