{"tools": {"searchPlaceholder": "Поиск инструментов...", "noToolsAvailable": "Нет доступных инструментов", "toolList": "Список инструментов", "functionDescription": "Описание функции", "path": "Путь", "pathPlaceholder": "Введите путь к файлу", "searchPattern": "Шаблон поиска", "searchPatternPlaceholder": "Введите регулярное выражение", "filePattern": "Шаблон файла", "filePatternPlaceholder": "Введите шаблон файла, например: *.md", "executeButton": "Выполнить", "resultTitle": "Результат выполнения", "runningTool": "Выполнение...", "loading": "Загрузка...", "error": "Ошибка загрузки", "available": "Доступно инструментов: {count}", "none": "Нет доступных инструментов", "title": "Инструменты MCP", "description": "Инструменты, предоставляемые сервером MCP", "loadError": "Не удалось загрузить инструменты", "parameters": "Параметры", "refresh": "Обновить", "disabled": "MCP отключен", "enableToUse": "Включите MCP для использования инструментов", "enabled": "Включить MCP", "enabledDescription": "Включить функциональность MCP для использования вызовов инструментов", "empty": "Пусто", "invalidJsonFormat": "Неверный формат JSON", "jsonInputPlaceholder": "Введите параметры в формате JSON", "input": "Входные параметры", "type": "Тип", "annotations": "Аннотации", "selectToolToDebug": "Выберите инструмент для отладки", "dialogDescription": "Отладка и тестирование инструментов, предоставляемых серверами MCP", "toolsCount": "инструменты", "availableTools": "Доступные инструменты", "invalidJson": "Неверный формат JSON", "inputHint": "Пожалуйста, введите параметры в формате JSON", "required": "Обязательно", "noDescription": "Нет описания"}, "addServer": "Добавить сервер", "addServerDialog": {"description": "Настройте новый сервер MCP", "title": "Добавить сервер"}, "confirmDelete": {"cancel": "Отмена", "confirm": "удалить", "description": "Вы уверены, что хотите удалить сервер {name}? Эта операция не может быть отменена.", "title": "Подтвердите удаление"}, "confirmRemoveServer": "Вы уверены, что хотите удалить сервер {name}? Эта операция не может быть отменена.", "default": "по умолчанию", "deleteServer": "Удалить сервер", "description": "Управление и настройка серверов и инструментов MCP (Model Context Protocol)", "editServer": "Отредактируйте сервер", "editServerDialog": {"description": "Редактировать конфигурацию сервера MCP", "title": "Отредактируйте сервер"}, "enableToAccess": "Сначала включите MCP для доступа к настройкам", "enabledDescription": "Включение или отключение функций и инструментов MCP", "enabledTitle": "Включить MCP", "isDefault": "Сервер по умолчанию", "noServersFound": "Сервер не найден", "removeDefault": "Удалить по умолчанию", "removeServer": "Удалить сервер", "removeServerDialog": {"title": "Удалить сервер"}, "resetConfirm": "восстанавливаться", "resetConfirmDescription": "Это действие восстанавливает все серверы по умолчанию при сохранении ваших индивидуальных серверов. Любые изменения на сервере по умолчанию будут потеряны.", "resetConfirmTitle": "Восстановить службу по умолчанию", "resetToDefault": "Восстановить службу по умолчанию", "running": "<PERSON><PERSON><PERSON>", "serverForm": {"add": "добавить в", "args": "параметр", "argsPlaceholder": "Введите параметры, разделенные пространствами", "argsRequired": "Параметры не могут быть пустыми", "autoApprove": "Автоматическое авторизация", "autoApproveAll": "все", "autoApproveHelp": "Выберите тип операции, который требует автоматической авторизации и выполнения без подтверждения пользователя", "autoApproveRead": "Читать", "autoApproveWrite": "Пис<PERSON><PERSON>ь", "baseUrl": "Основной URL", "baseUrlPlaceholder": "Введите базовый URL -адрес сервера (например: http: // localhost: 3000)", "cancel": "Отмена", "command": "Зак<PERSON>з", "commandPlaceholder": "Введите команду", "commandRequired": "Команда не может быть пустой", "configImported": "Импорт конфигурации успешно", "description": "описывать", "descriptionPlaceholder": "Введите описание сервера", "descriptions": "описывать", "descriptionsPlaceholder": "Введите описание сервера", "env": "Переменные среды", "envInvalid": "Переменные среды должны быть в действительном формате JSON", "envPlaceholder": "Введите переменные среды в формате JSON", "icon": "икона", "iconPlaceholder": "Введите значок", "icons": "икона", "iconsPlaceholder": "Введите значок", "jsonConfig": "Конфигурация JSON", "jsonConfigExample": "Пример конфигурации JSON", "jsonConfigIntro": "Вы можете напрямую вставить конфигурацию JSON или выбрать настройку сервера вручную.", "jsonConfigPlaceholder": "Пожалуйста, вставьте конфигурацию формата JSON на сервере MCP", "name": "Имя сервера", "namePlaceholder": "Введите имя сервера", "nameRequired": "Имя сервера не может быть пустым", "parseAndContinue": "Проанализируйте и продолжайте", "parseError": "Ошибка синтаксического анализа", "parseSuccess": "Конфигурация успешно", "skipToManual": "Пропустить ручную конфигурацию", "submit": "представлять на рассмотрение", "type": "Тип сервера", "typeInMemory": "Память", "typePlaceholder": "Выберите тип сервера", "typeSse": "Сервер отправляет события", "typeStdio": "Стандартный вход и вывод", "update": "обновлять", "addFolder": "Добавить папку", "folders": "Список папок", "noFoldersSelected": "Не было выбрано папки", "selectFolder": "Выберите папку", "selectFolderError": "Не удалось выбрать папку"}, "serverList": "Список серверов", "setAsDefault": "Установить как сервер по умолчанию", "setDefault": "Установить по умолчанию", "startServer": "Запустить сервер", "stopServer": "Остановите сервер", "stopped": "Остановился", "tabs": {"servers": "сервер", "tools": "инструмент"}, "title": "Настройки MCP", "inmemory": {"Artifacts": {"desc": "Сделайте свой DeepChat выводы диверсифицированным артефактом", "name": "Artifacts"}, "bochaSearch": {"desc": "Bocha Search API https://open.bochaai.com/", "name": "Bocha Search"}, "buildInFileSystem": {"desc": "Включить DeepChat манипулировать локальными файлами", "name": "Файловая система"}, "mediaServer": {"desc": "Позволяет всем моделям DeepChat обрабатывать изображения, видео и аудиофайлы", "name": "Медиа-сервис"}, "braveSearch": {"desc": "Храбрый поиск API https://brave.com/search/api/", "name": "Смелый поиск"}, "powerpack": {"desc": "Предоставьте расширенные возможности, такие как временные запросы, получение веб -информации и безопасное выполнение кода для любой большой модели, чтобы у модели были более мощные и точные возможности получения информации.", "name": "Усовершенствование инструментов"}, "difyKnowledge": {"name": "Поиск в базе знаний Dify", "desc": "Поисковая служба базы знаний Dify, может выполнять поиск по содержимому базы знаний Dify"}, "ragflowKnowledge": {"name": "Поиск в базе знаний RAGFlow", "desc": "Служба поиска в базе знаний RAGFlow, может выполнять поиск по содержимому базы знаний RAGFlow"}, "fastGptKnowledge": {"name": "Поиск в базе знаний FastGPT", "desc": "Служба поиска в базе знаний FastGPT, может выполнять поиск по содержимому базы знаний FastGPT"}, "deepchat-inmemory/custom-prompts-server": {"desc": "DeepChat встроенный заказ", "name": "Пользовательские быстрые слова"}, "deepchat-inmemory/deep-research-server": {"desc": "Встроенная подробная исследовательская служба DeepChat на основе поиска BOCHA (обратите внимание, что необходимы для использования длинных контекстов, а модели с недостаточным контекстом могут потерпеть неудачу)", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "Автоматическая генерация шаблонов подсказок", "desc": "Автоматически выбирает наиболее подходящий пользовательский шаблон подсказки на основе ввода пользователя и интеллектуально заполняет шаблон подсказки."}, "deepchat-inmemory/conversation-search-server": {"name": "Поиск истории разговоров", "desc": "Встроенная служба поиска истории разговоров DeepChat, может искать записи исторических разговоров и содержимое сообщений"}, "deepchat-inmemory/meeting-server": {"name": "Мультиагентная встреча", "desc": "Встроенный сервис встреч DeepChat позволяет организовывать и проводить обсуждения между несколькими агентами."}, "builtinKnowledge": {"desc": "Встроенная служба поиска базы знаний DeepChat, которая может искать содержание встроенной базы знаний DeepChat", "name": "Встроенный поиск базы знаний"}, "deepchat/apple-server": {"desc": "Пусть модель работает системные функции MacOS, такие как календарь, контакты, электронные письма, карты, записки, напоминания и другие системы", "name": "MacOS System Assistant"}}, "prompts": {"noPromptsAvailable": "Нет подсказок", "noDescription": "Пока нет описания", "selectPrompt": "Показано здесь", "parameters": "Быстрые параметры", "input": "Входные параметры", "runningPrompt": "Подсказка получает", "executeButton": "Нажмите, чтобы получить", "resultTitle": "Краткие данные", "invalidJson": "Неверный формат JSON", "parametersHint": "Пожалуйста, введите параметры в формате JSON, поддержите автоматическое форматирование", "resetToDefault": "Сбросить параметры по умолчанию", "dialogDescription": "Отладка и тестирование промптов, предоставляемых серверами MCP"}, "resources": {"noResourcesAvailable": "Нет ресурсов", "selectResource": "Показать ресурсы содержание здесь", "loading": "загрузка", "loadContent": "Получить контент ресурсов", "pleaseSelect": "Нажмите, чтобы получить данные ресурсов отображения", "dialogDescription": "Просмотр и отображение ресурсов, предоставляемых серверами MCP"}, "errors": {"loadConfigFailed": "Не удалось загрузить конфигурацию MCP", "setEnabledFailed": "Не удалось установить состояние включения MCP", "getServerStatusFailed": "Не удалось получить статус сервера {serverName}", "addServerFailed": "Не удалось добавить сервер", "updateServerFailed": "Не удалось обновить сервер", "removeServerFailed": "Не удалось удалить сервер", "maxDefaultServersReached": "Достигнуто максимальное количество серверов по умолчанию (30)", "toggleDefaultServerFailed": "Не удалось переключить статус сервера по умолчанию", "resetToDefaultFailed": "Не удалось сбросить настройки серверов по умолчанию", "toggleServerFailed": "Не удалось переключить статус сервера {serverName}", "loadToolsFailed": "Не удалось загрузить инструменты", "loadPromptsFailed": "Не удалось загрузить промпты", "loadResourcesFailed": "Не удалось загрузить ресурсы", "callToolFailed": "Не удалось вызвать инструмент {toolName}", "toolCallError": "Ошибка вызова инструмента: {error}", "mcpDisabled": "MCP отключен", "getPromptFailed": "Не удалось получить промпт", "readResourceFailed": "Не удалось прочитать ресурс"}, "market": {"apiKeyPlaceholder": "Введите ключ McProuter API", "apiKeyRequiredDesc": "Пожалуйста, заполните клавишу McProuter API сначала перед установкой", "apiKeyRequiredTitle": "Требует ключа API", "browseBuiltin": "Просмотрите встроенный рынок MCP", "builtinTitle": "MCP Market", "empty": "Пока нет услуг", "install": "Установить", "installFailed": "Установка не удалась", "installSuccess": "Установка успешно", "installed": "Установлен", "keyGuide": "Получите ключ", "keyHelpEnd": "После подачи заявки на ключ API заполните поле ввода выше", "keyHelpText": "Пожалуйста, приезжайте первым", "loadMore": "Загружать больше", "noMore": "Больше не надо", "poweredBy": "Оборудован от McProuter", "pullDownToLoad": "Продолжайте падать, чтобы загружать больше"}}